{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if object %}تعديل نتيجة مادة{% else %}إضافة نتيجة مادة{% endif %} - نظام إدارة طلاب رواق العلوم الشرعية والعربية
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h2 mb-0 text-primary fw-bold">
                <i class="fas fa-edit me-2"></i>
                {% if object %}تعديل نتيجة مادة{% else %}إضافة نتيجة مادة{% endif %}
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'students:course_result_list' %}">نتائج المواد</a></li>
                    <li class="breadcrumb-item active" aria-current="page">
                        {% if object %}تعديل نتيجة مادة{% else %}إضافة نتيجة مادة{% endif %}
                    </li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{% url 'students:course_result_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة لنتائج المواد
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body p-4">
                    <h5 class="card-title mb-4">
                        <i class="fas fa-clipboard-list me-2"></i>
                        {% if object %}تعديل نتيجة مادة{% else %}إضافة نتيجة مادة جديدة{% endif %}
                    </h5>

                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="{{ form.student.id_for_label }}" class="form-label">الطالب</label>
                            {{ form.student }}
                            <div class="form-text text-muted">
                                حدد الطالب لإضافة نتيجة له
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.course.id_for_label }}" class="form-label">المادة</label>
                            {{ form.course }}
                            <div class="form-text text-muted">
                                حدد المادة الدراسية
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.academic_year.id_for_label }}" class="form-label">العام الدراسي</label>
                            {{ form.academic_year }}
                            <div class="form-text text-muted">
                                حدد العام الدراسي للنتيجة (مثال: 2022-2023)
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.score.id_for_label }}" class="form-label">الدرجة</label>
                            {{ form.score }}
                            <div class="form-text text-muted">
                                أدخل درجة الطالب في المادة
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if object %}حفظ التعديلات{% else %}إضافة النتيجة{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#id_student').addClass('form-select');
    $('#id_course').addClass('form-select');
    $('#id_academic_year').addClass('form-control');
    $('#id_score').addClass('form-control');
});
</script>
{% endblock %} 