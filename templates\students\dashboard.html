{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة التحكم - نظام إدارة طلاب رواق العلوم الشرعية والعربية{% endblock %}

{% block content %}
<div class="row mb-4 align-items-center">
    <div class="col-md-8">
        <h1 class="h2 mb-0 text-primary fw-bold">لوحة التحكم</h1>
        <p class="text-muted">نظرة عامة على نظام إدارة طلاب رواق العلوم الشرعية والعربية</p>
    </div>
    <div class="col-md-4 text-md-end">
        <a href="{% url 'students:student_add' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle"></i> إضافة طالب جديد
        </a>
    </div>
</div>

<!-- Search Form -->
<div class="card shadow-sm border-0 mb-4">
    <div class="card-body p-3">
        <form method="get" action="/students/search/" class="mb-0">
            <div class="row g-3 align-items-center">
                <div class="col-md-10">
                    <div class="input-group">
                        <span class="input-group-text bg-light"><i class="fas fa-search"></i></span>
                        <input type="text" name="q" class="form-control" placeholder="بحث عن طالب بالاسم أو الكود أو رقم الجلوس أو الرقم القومي" required>
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">بحث</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-5">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-dashboard h-100">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0 text-primary me-3">
                        <i class="fas fa-user-graduate fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="card-title mb-0">إجمالي الطلاب</h6>
                        <small class="text-muted">في جميع المراحل</small>
                    </div>
                </div>
                <h2 class="display-5 fw-bold text-center mb-0">{{ total_students }}</h2>
                <div class="text-center mt-3">
                    <a href="{% url 'students:student_list' %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i> عرض الطلاب
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- يمكن إضافة المزيد من البطاقات الإحصائية هنا -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-dashboard h-100">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0 text-success me-3">
                        <i class="fas fa-clipboard-list fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="card-title mb-0">النتائج</h6>
                        <small class="text-muted">إدارة نتائج الطلاب</small>
                    </div>
                </div>
                <div class="text-center mt-4 mb-2">
                    <a href="{% url 'students:course_result_list' %}" class="btn btn-sm btn-outline-success mb-2 me-2">
                        <i class="fas fa-list"></i> نتائج المواد
                    </a>
                    <a href="{% url 'students:results_list' %}" class="btn btn-sm btn-outline-success mb-2">
                        <i class="fas fa-certificate"></i> النتائج النهائية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-dashboard h-100">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0 text-info me-3">
                        <i class="fas fa-level-up-alt fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="card-title mb-0">ترقية الطلاب</h6>
                        <small class="text-muted">التصعيد للمستويات الأعلى</small>
                    </div>
                </div>
                <div class="text-center mt-4 mb-2">
                    <a href="{% url 'students:promote_students' %}" class="btn btn-sm btn-outline-info mb-2">
                        <i class="fas fa-level-up-alt"></i> ترقية الطلاب
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-dashboard h-100">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0 text-warning me-3">
                        <i class="fas fa-cloud-upload-alt fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="card-title mb-0">استيراد وتصدير</h6>
                        <small class="text-muted">بيانات الطلاب والنتائج</small>
                    </div>
                </div>
                <div class="text-center mt-4 mb-2">
                    <a href="{% url 'students:export_students' %}" class="btn btn-sm btn-outline-warning mb-2 me-2">
                        <i class="fas fa-download"></i> تصدير الطلاب
                    </a>
                    <a href="{% url 'students:import_students' %}" class="btn btn-sm btn-outline-warning mb-2">
                        <i class="fas fa-file-import"></i> استيراد طلاب
                    </a>
                    <div class="mt-2">
                        <a href="{% url 'students:bulk_upload_results' %}" class="btn btn-sm btn-outline-warning mb-2 me-2">
                            <i class="fas fa-upload"></i> رفع نتائج
                        </a>
                        <a href="{% url 'students:import_results' %}" class="btn btn-sm btn-outline-warning mb-2">
                            <i class="fas fa-file-import"></i> استيراد نتائج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Students by Level -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    الطلاب حسب المستوى
                </h5>
                <div class="btn-group" role="group">
                    <a href="{% url 'students:student_list' %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i> عرض جميع الطلاب
                    </a>
                    {% if total_students > 0 %}
                    <a href="{% url 'students:delete_all_students' %}" class="btn btn-sm btn-outline-danger"
                       onclick="return confirm('هل أنت متأكد من الرغبة في حذف جميع الطلاب؟')">
                        <i class="fas fa-trash-alt"></i> حذف الكل
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="ps-4">المرحلة</th>
                                <th>المستوى</th>
                                <th>عدد الطلاب</th>
                                <th class="text-center">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for level in students_by_level %}
                            <tr>
                                <td class="ps-4">
                                    <span class="badge bg-light text-dark">{{ level.stage.name }}</span>
                                </td>
                                <td>{{ level.name }}</td>

                                <td>
                                    <span class="badge bg-primary rounded-pill">{{ level.student_count }}</span>
                                </td>
                                <td class="text-center">
                                    <a href="{% url 'students:student_list' %}?level={{ level.id }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> عرض الطلاب
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center py-4">لا توجد مستويات مسجلة بعد</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Access -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    الوصول السريع
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-lg-3 col-md-4 col-6 mb-4">
                        <a href="{% url 'students:student_add' %}" class="text-decoration-none quick-access-item d-block">
                            <div class="quick-access-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <p class="mb-0">إضافة طالب جديد</p>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-4 col-6 mb-4">
                        <a href="{% url 'students:course_result_add' %}" class="text-decoration-none quick-access-item d-block">
                            <div class="quick-access-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <p class="mb-0">إضافة نتيجة</p>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-4 col-6 mb-4">
                        <a href="{% url 'students:generate_results' %}" class="text-decoration-none quick-access-item d-block">
                            <div class="quick-access-icon">
                                <i class="fas fa-clipboard-check"></i>
                            </div>
                            <p class="mb-0">إنشاء النتائج النهائية</p>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-4 col-6 mb-4">
                        <a href="{% url 'students:promote_students' %}" class="text-decoration-none quick-access-item d-block">
                            <div class="quick-access-icon">
                                <i class="fas fa-level-up-alt"></i>
                            </div>
                            <p class="mb-0">ترقية الطلاب</p>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-4 col-6 mb-4">
                        <a href="{% url 'students:bulk_upload_results' %}" class="text-decoration-none quick-access-item d-block">
                            <div class="quick-access-icon">
                                <i class="fas fa-upload"></i>
                            </div>
                            <p class="mb-0">رفع نتائج بالجملة</p>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-4 col-6 mb-4">
                        <a href="{% url 'students:course_result_list' %}" class="text-decoration-none quick-access-item d-block">
                            <div class="quick-access-icon">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            <p class="mb-0">نتائج المواد</p>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-4 col-6 mb-4">
                        <a href="{% url 'students:results_list' %}" class="text-decoration-none quick-access-item d-block">
                            <div class="quick-access-icon">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <p class="mb-0">النتائج النهائية</p>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-4 col-6 mb-4">
                        <a href="{% url 'students:import_students' %}" class="text-decoration-none quick-access-item d-block">
                            <div class="quick-access-icon">
                                <i class="fas fa-file-import"></i>
                            </div>
                            <p class="mb-0">استيراد طلاب</p>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-4 col-6 mb-4">
                        <a href="{% url 'students:import_results' %}" class="text-decoration-none quick-access-item d-block">
                            <div class="quick-access-icon">
                                <i class="fas fa-file-import"></i>
                            </div>
                            <p class="mb-0">استيراد نتائج</p>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-4 col-6 mb-4">
                        <a href="{% url 'admin:index' %}" class="text-decoration-none quick-access-item d-block">
                            <div class="quick-access-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <p class="mb-0">لوحة الإدارة</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}