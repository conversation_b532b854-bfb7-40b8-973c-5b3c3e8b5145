{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if object %}تعديل الدفعة{% else %}إضافة دفعة جديدة{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'students:batch_list' %}">إدارة الدفعات</a></li>
                    <li class="breadcrumb-item active" aria-current="page">
                        {% if object %}تعديل الدفعة{% else %}إضافة دفعة جديدة{% endif %}
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users"></i>
                        {% if object %}تعديل الدفعة: {{ object.name }}{% else %}إضافة دفعة جديدة{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">اسم الدفعة *</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.start_date.id_for_label }}" class="form-label">تاريخ البداية</label>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                    <div class="text-danger small">{{ form.start_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.end_date.id_for_label }}" class="form-label">تاريخ النهاية</label>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                    <div class="text-danger small">{{ form.end_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        دفعة نشطة
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'students:batch_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> رجوع
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if object %}تحديث الدفعة{% else %}إضافة الدفعة{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين حقول التاريخ
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        input.classList.add('form-control');
    });
    
    // تحسين checkbox
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.classList.add('form-check-input');
    });
    
    // تحسين text inputs
    const textInputs = document.querySelectorAll('input[type="text"]');
    textInputs.forEach(input => {
        input.classList.add('form-control');
    });
});
</script>
{% endblock %}
