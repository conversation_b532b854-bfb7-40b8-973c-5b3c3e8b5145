{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{% if form.instance.id %}تعديل بيانات طالب{% else %}إضافة طالب جديد{% endif %} - نظام إدارة طلاب رواق العلوم الشرعية والعربية{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h2 mb-0 text-primary fw-bold">
                {% if form.instance.id %}
                <i class="fas fa-user-edit me-2"></i>تعديل بيانات الطالب
                {% else %}
                <i class="fas fa-user-plus me-2"></i>إضافة طالب جديد
                {% endif %}
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'students:student_list' %}">قائمة الطلاب</a></li>
                    <li class="breadcrumb-item active" aria-current="page">
                        {% if form.instance.id %}تعديل بيانات طالب{% else %}إضافة طالب جديد{% endif %}
                    </li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{% url 'students:student_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة لقائمة الطلاب
            </a>
        </div>
    </div>

    <div class="card shadow-sm border-0">
        <div class="card-body p-4">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="row">
                    <div class="col-md-12 mb-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            يرجى ملء كافة البيانات المطلوبة. الحقول المميزة بعلامة * هي حقول إلزامية.
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12 mb-3">
                        {{ form.full_name|as_crispy_field }}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        {{ form.code|as_crispy_field }}
                    </div>
                    <div class="col-md-4 mb-3">
                        {{ form.current_seat_number|as_crispy_field }}
                    </div>
                    <div class="col-md-4 mb-3">
                        {{ form.gender|as_crispy_field }}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        {{ form.stage|as_crispy_field }}
                    </div>
                    <div class="col-md-6 mb-3">
                        {{ form.level|as_crispy_field }}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        {{ form.madhhab|as_crispy_field }}
                    </div>
                    <div class="col-md-6 mb-3">
                        {{ form.study_type|as_crispy_field }}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        {{ form.national_id|as_crispy_field }}
                    </div>
                    <div class="col-md-6 mb-3">
                        {{ form.phone_number|as_crispy_field }}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        {{ form.vision_status|as_crispy_field }}
                    </div>
                    <div class="col-md-4 mb-3">
                        {{ form.special_needs|as_crispy_field }}
                    </div>
                    <div class="col-md-4 mb-3">
                        {{ form.governorate|as_crispy_field }}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        {{ form.previous_seat_number|as_crispy_field }}
                    </div>
                    <div class="col-md-6 mb-3">
                        {{ form.enrollment_status|as_crispy_field }}
                    </div>
                </div>
                
                <hr class="my-4">
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{% url 'students:student_list' %}" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-times-circle"></i> إلغاء
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 
                        {% if form.instance.id %}حفظ التغييرات{% else %}إضافة الطالب{% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Script to dynamically update the level options based on selected stage
    $(document).ready(function() {
        $('#id_stage').change(function() {
            var stageId = $(this).val();
            if (stageId) {
                $.ajax({
                    url: '{% url "students:ajax_load_levels" %}',
                    data: {
                        'stage_id': stageId
                    },
                    success: function(data) {
                        $('#id_level').html(data);
                    }
                });
            } else {
                $('#id_level').html('<option value="">---------</option>');
            }
        });
    });
</script>
{% endblock %} 