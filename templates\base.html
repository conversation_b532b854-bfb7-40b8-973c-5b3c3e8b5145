<!DOCTYPE html>
{% load static %}
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة طلاب رواق العلوم الشرعية والعربية بالأزهر الشريف{% endblock %}</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <!-- الشعار الأيمن -->
            <div class="logo-container d-none d-lg-flex">
                <img src="/static/img/azhar-logo.png" alt="شعار الأزهر" width="60" height="60" style="margin-left: 10px; border-radius: 50%; background-color: white; padding: 5px;">
            </div>

            <a class="navbar-brand ms-3 me-3" href="{% url 'students:dashboard' %}">
                رواق العلوم الشرعية والعربية
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                {% if user.is_authenticated %}
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" href="{% url 'students:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'search_student' %}active{% endif %}" href="/students/search/">
                            <i class="fas fa-search"></i> البحث عن طالب
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if 'student_' in request.resolver_match.url_name %}active{% endif %}" href="#" id="studentsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-graduate"></i> الطلاب
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="studentsDropdown">
                            <li><a class="dropdown-item" href="{% url 'students:student_list' %}">قائمة الطلاب</a></li>
                            <li><a class="dropdown-item" href="{% url 'students:student_add' %}">إضافة طالب جديد</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'students:export_students' %}">تصدير بيانات الطلاب</a></li>
                            <li><a class="dropdown-item" href="{% url 'students:import_students' %}">استيراد بيانات الطلاب</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if 'result' in request.resolver_match.url_name %}active{% endif %}" href="#" id="resultsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-clipboard-list"></i> النتائج
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="resultsDropdown">
                            <li><a class="dropdown-item" href="{% url 'students:course_result_list' %}">نتائج المواد</a></li>
                            <li><a class="dropdown-item" href="{% url 'students:course_result_add' %}">إضافة نتيجة</a></li>
                            <li><a class="dropdown-item" href="{% url 'students:bulk_upload_results' %}">رفع نتائج بالجملة</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'students:results_list' %}">النتائج النهائية</a></li>
                            <li><a class="dropdown-item" href="{% url 'students:generate_results' %}">إنشاء النتائج النهائية</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'students:import_results' %}">استيراد نتائج</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if 'batch' in request.resolver_match.url_name %}active{% endif %}" href="#" id="batchDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-users"></i> إدارة الدفعات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="batchDropdown">
                            <li><a class="dropdown-item" href="{% url 'students:batch_list' %}"><i class="fas fa-list me-2"></i>قائمة الدفعات</a></li>
                            <li><a class="dropdown-item" href="{% url 'students:batch_add' %}"><i class="fas fa-plus me-2"></i>إضافة دفعة جديدة</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'promote_students' %}active{% endif %}" href="{% url 'students:promote_students' %}">
                            <i class="fas fa-level-up-alt"></i> ترقية الطلاب
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i> {{ user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="{% url 'admin:index' %}"><i class="fas fa-cog me-2"></i> لوحة الإدارة</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'logout' %}"><i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
                {% else %}
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}">
                            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                        </a>
                    </li>
                </ul>
                {% endif %}
            </div>

            <!-- الشعار الأيسر -->
            <div class="logo-container d-none d-lg-flex">
                <img src="/static/img/rewaq.png" alt="شعار الرواق" width="60" height="60" style="margin-right: 10px; border-radius: 50%; background-color: white; padding: 5px;">
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4 mb-5">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show shadow-sm" role="alert">
                    {% if message.tags == 'success' %}
                        <i class="fas fa-check-circle me-2"></i>
                    {% elif message.tags == 'warning' %}
                        <i class="fas fa-exclamation-triangle me-2"></i>
                    {% elif message.tags == 'error' %}
                        <i class="fas fa-times-circle me-2"></i>
                    {% elif message.tags == 'info' %}
                        <i class="fas fa-info-circle me-2"></i>
                    {% endif %}
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="py-4 mt-auto bg-light">
        <div class="container">
            <div class="d-flex justify-content-between">
                <div>
                    <p class="mb-0 text-muted">&copy; {{ current_year }} رواق العلوم الشرعية والعربية. جميع الحقوق محفوظة.</p>
                </div>
                <div>
                    <p class="mb-0 text-muted">تطوير فريق تكنولوجيا المعلومات</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="{% static 'js/main.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>