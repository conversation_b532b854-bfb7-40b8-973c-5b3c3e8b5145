from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Count, Q
from django.http import HttpResponse
from django.utils.translation import gettext_lazy as _

from .models import Student, Course, CourseResult, StudentResult, Level, Stage, AcademicYear
from .forms import (
    StudentForm, CourseResultForm, StudentResultsForm,
    StudentPromotionForm, BulkResultsUploadForm, ImportStudentsForm, ImportResultsForm
)
from .utils import (
    export_students_to_excel, export_results_to_excel, export_student_certificate,
    import_students_from_excel, import_results_from_excel, export_level_results_template
)

import csv
import pandas as pd
from io import TextIOWrapper

# Dashboard Views
@login_required
def dashboard(request):
    total_students = Student.objects.count()
    students_by_level = Level.objects.annotate(
        student_count=Count('student')
    ).order_by('stage', 'name')

    context = {
        'total_students': total_students,
        'students_by_level': students_by_level,
    }
    return render(request, 'students/dashboard.html', context)

# Student Management Views
class StudentListView(LoginRequiredMixin, ListView):
    model = Student
    template_name = 'students/student_list.html'
    context_object_name = 'students'
    paginate_by = 20

    def get_queryset(self):
        queryset = super().get_queryset()
        level_id = self.request.GET.get('level')
        enrollment_status = self.request.GET.get('status')
        search_query = self.request.GET.get('q')

        if level_id:
            queryset = queryset.filter(level_id=level_id)
        if enrollment_status:
            queryset = queryset.filter(enrollment_status=enrollment_status)
        if search_query:
            queryset = queryset.filter(
                Q(full_name__icontains=search_query) |
                Q(code__icontains=search_query) |
                Q(current_seat_number__icontains=search_query) |
                Q(national_id__icontains=search_query)
            )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['levels'] = Level.objects.all()
        context['enrollment_statuses'] = Student.ENROLLMENT_CHOICES
        return context

class StudentDetailView(LoginRequiredMixin, DetailView):
    model = Student
    template_name = 'students/student_detail.html'
    context_object_name = 'student'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        student = self.get_object()
        context['course_results'] = student.results.all().order_by('-academic_year', 'course__name')
        context['final_results'] = student.final_results.all().order_by('-academic_year')
        return context

class StudentCreateView(LoginRequiredMixin, CreateView):
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    success_url = reverse_lazy('students:student_list')

    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة الطالب بنجاح'))
        return super().form_valid(form)

class StudentUpdateView(LoginRequiredMixin, UpdateView):
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    success_url = reverse_lazy('students:student_list')

    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث بيانات الطالب بنجاح'))
        return super().form_valid(form)

class StudentDeleteView(LoginRequiredMixin, DeleteView):
    model = Student
    template_name = 'students/student_confirm_delete.html'
    success_url = reverse_lazy('students:student_list')

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, _('تم حذف الطالب بنجاح'))
        return super().delete(request, *args, **kwargs)

# Course Results Views
class CourseResultCreateView(LoginRequiredMixin, CreateView):
    model = CourseResult
    form_class = CourseResultForm
    template_name = 'students/courseresult_form.html'
    success_url = reverse_lazy('students:course_result_list')

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        student_id = self.request.GET.get('student')
        if student_id:
            form.fields['student'].initial = student_id
            student = Student.objects.get(id=student_id)
            form.fields['course'].queryset = Course.objects.filter(level=student.level)
        return form

    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة نتيجة المادة بنجاح'))
        return super().form_valid(form)

class CourseResultUpdateView(LoginRequiredMixin, UpdateView):
    model = CourseResult
    form_class = CourseResultForm
    template_name = 'students/courseresult_form.html'
    success_url = reverse_lazy('students:course_result_list')

    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث نتيجة المادة بنجاح'))
        return super().form_valid(form)

class CourseResultListView(LoginRequiredMixin, ListView):
    model = CourseResult
    template_name = 'students/courseresult_list.html'
    context_object_name = 'results'
    paginate_by = 50

    def get_queryset(self):
        queryset = super().get_queryset()
        course_id = self.request.GET.get('course')
        academic_year = self.request.GET.get('academic_year')
        level_id = self.request.GET.get('level')
        search_query = self.request.GET.get('q')

        if course_id:
            queryset = queryset.filter(course_id=course_id)
        if academic_year:
            queryset = queryset.filter(academic_year=academic_year)
        if level_id:
            queryset = queryset.filter(student__level_id=level_id)
        if search_query:
            queryset = queryset.filter(
                Q(student__full_name__icontains=search_query) |
                Q(student__code__icontains=search_query)
            )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['courses'] = Course.objects.all()
        context['levels'] = Level.objects.all()
        context['years'] = CourseResult.objects.values_list('academic_year', flat=True).distinct()
        return context

@login_required
def bulk_upload_results(request):
    # تصدير نموذج إذا تم طلب ذلك
    if request.method == 'GET' and 'export_template' in request.GET:
        stage_id = request.GET.get('stage')
        level_id = request.GET.get('level')
        academic_year = request.GET.get('academic_year')

        if level_id:
            level = get_object_or_404(Level, id=level_id)
            return export_level_results_template(level, academic_year)
        else:
            messages.error(request, 'يرجى اختيار المرحلة والمستوى أولاً')

    if request.method == 'POST':
        form = BulkResultsUploadForm(request.POST, request.FILES)
        if form.is_valid():
            stage = form.cleaned_data['stage']
            level = form.cleaned_data['level']
            academic_year = form.cleaned_data['academic_year']
            results_file = request.FILES.get('results_file')

            if not results_file:
                messages.error(request, 'يرجى اختيار ملف النتائج')
                return render(request, 'students/bulk_upload_results.html', {'form': form})

            # معالجة ملف Excel
            if results_file.name.endswith(('.xls', '.xlsx')):
                try:
                    df = pd.read_excel(results_file)

                    # التحقق من وجود الأعمدة المطلوبة
                    required_columns = ['رقم الجلوس', 'اسم الطالب']
                    missing_columns = [col for col in required_columns if col not in df.columns]

                    if missing_columns:
                        messages.error(request, f'الأعمدة التالية مفقودة: {", ".join(missing_columns)}')
                        return render(request, 'students/bulk_upload_results.html', {'form': form})

                    # الحصول على جميع المواد لهذا المستوى
                    courses = Course.objects.filter(level=level)

                    imported_count = 0
                    updated_count = 0
                    errors = []

                    for _, row in df.iterrows():
                        try:
                            seat_number = str(row['رقم الجلوس']).strip()

                            # البحث عن الطالب برقم الجلوس
                            try:
                                student = Student.objects.get(
                                    current_seat_number=seat_number,
                                    level=level
                                )
                            except Student.DoesNotExist:
                                errors.append(f'لم يتم العثور على طالب برقم جلوس: {seat_number}')
                                continue

                            # معالجة درجات كل مادة
                            for course in courses:
                                course_column = None
                                # البحث عن عمود المادة في الملف
                                for col in df.columns:
                                    if course.name in col:
                                        course_column = col
                                        break

                                if course_column and not pd.isna(row[course_column]):
                                    try:
                                        score = int(float(row[course_column]))

                                        # التحقق من صحة الدرجة
                                        if 0 <= score <= course.max_score:
                                            course_result, created = CourseResult.objects.update_or_create(
                                                student=student,
                                                course=course,
                                                academic_year=academic_year,
                                                defaults={'score': score}
                                            )

                                            if created:
                                                imported_count += 1
                                            else:
                                                updated_count += 1
                                        else:
                                            errors.append(f'درجة غير صحيحة للطالب {student.full_name} في مادة {course.name}: {score}')

                                    except (ValueError, TypeError):
                                        errors.append(f'درجة غير صالحة للطالب {student.full_name} في مادة {course.name}')

                        except Exception as e:
                            errors.append(f'خطأ في معالجة بيانات الطالب: {str(e)}')

                    # إعداد رسالة النجاح
                    success_message = f'تم استيراد {imported_count} نتيجة جديدة وتحديث {updated_count} نتيجة'

                    if errors:
                        error_message = f'الأخطاء ({len(errors)}): {"; ".join(errors[:5])}'
                        if len(errors) > 5:
                            error_message += f' و{len(errors) - 5} أخطاء أخرى'
                        messages.warning(request, f'{success_message}. {error_message}')
                    else:
                        messages.success(request, success_message)

                    return redirect('students:course_result_list')

                except Exception as e:
                    messages.error(request, f'خطأ في قراءة الملف: {str(e)}')
            else:
                messages.error(request, 'يرجى رفع ملف Excel فقط')
    else:
        form = BulkResultsUploadForm()

    context = {
        'form': form,
        'stages': Stage.objects.all(),
        'levels': Level.objects.all(),
    }

    return render(request, 'students/bulk_upload_results.html', context)

# Generate Results View
@login_required
def generate_results(request):
    if request.method == 'POST':
        form = StudentResultsForm(request.POST)
        if form.is_valid():
            academic_year = form.cleaned_data['academic_year']
            level = form.cleaned_data['level']

            # Get all students in this level
            students = Student.objects.filter(level=level)

            # Get all courses for this level
            courses = Course.objects.filter(level=level)

            # Process each student
            for student in students:
                course_results = CourseResult.objects.filter(
                    student=student,
                    course__in=courses,
                    academic_year=academic_year
                )

                # Check if all results are available
                if course_results.count() < courses.count():
                    messages.warning(
                        request,
                        _(f'الطالب {student.full_name} ليس لديه نتائج لجميع المواد')
                    )
                    continue

                # Count failed courses
                failed_courses = []
                for result in course_results:
                    if result.score < result.course.pass_score:
                        failed_courses.append(result.course)

                # Determine result status
                if len(failed_courses) == 0:
                    result_status = 'ناجح ومنقول'
                elif len(failed_courses) <= 2:
                    result_status = 'منقول بمواد'
                else:
                    result_status = 'باقي للإعادة'

                # Create or update student result
                student_result, created = StudentResult.objects.update_or_create(
                    student=student,
                    level=level,
                    academic_year=academic_year,
                    defaults={'result': result_status}
                )

                # Update failed courses
                student_result.failed_courses.set(failed_courses)

            messages.success(request, _('تم إنشاء النتائج بنجاح'))
            return redirect('students:results_list')
    else:
        form = StudentResultsForm()

    return render(request, 'students/generate_results.html', {'form': form})

class StudentResultListView(LoginRequiredMixin, ListView):
    model = StudentResult
    template_name = 'students/studentresult_list.html'
    context_object_name = 'results'
    paginate_by = 50

    def get_queryset(self):
        queryset = super().get_queryset()
        level_id = self.request.GET.get('level')
        academic_year = self.request.GET.get('academic_year')
        result_status = self.request.GET.get('status')
        search_query = self.request.GET.get('q')

        if level_id:
            queryset = queryset.filter(level_id=level_id)
        if academic_year:
            queryset = queryset.filter(academic_year=academic_year)
        if result_status:
            queryset = queryset.filter(result=result_status)
        if search_query:
            queryset = queryset.filter(
                Q(student__full_name__icontains=search_query) |
                Q(student__code__icontains=search_query)
            )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['levels'] = Level.objects.all()
        # الحصول على السنوات الدراسية من جدول AcademicYear
        context['years'] = AcademicYear.objects.values_list('year', flat=True).order_by('-year')

        # إضافة بيانات المواد والدرجات
        level_id = self.request.GET.get('level')
        academic_year = self.request.GET.get('academic_year')

        if level_id and academic_year:
            level = Level.objects.get(id=level_id)
            courses = Course.objects.filter(level=level).order_by('name')
            context['courses'] = courses

            # إنشاء قائمة للنتائج مع الدرجات
            results_with_scores = []
            for result in context['results']:
                student_data = {
                    'result': result,
                    'course_scores': []
                }

                # الحصول على درجات الطالب في جميع المواد
                for course in courses:
                    try:
                        course_result = CourseResult.objects.get(
                            student=result.student,
                            course=course,
                            academic_year=academic_year
                        )
                        if course_result.score < course.pass_score:
                            score_data = {
                                'course': course,
                                'score': course_result.score,
                                'status': 'راسب',
                                'class': 'text-danger'
                            }
                        else:
                            score_data = {
                                'course': course,
                                'score': course_result.score,
                                'status': 'ناجح',
                                'class': 'text-success'
                            }
                    except CourseResult.DoesNotExist:
                        # إذا لم توجد درجة للمادة، يعتبر راسب
                        score_data = {
                            'course': course,
                            'score': 'غ',
                            'status': 'راسب',
                            'class': 'text-danger'
                        }

                    student_data['course_scores'].append(score_data)

                results_with_scores.append(student_data)

            context['results_with_scores'] = results_with_scores
        context['result_statuses'] = StudentResult.RESULT_CHOICES
        return context

# Student Promotion Views
@login_required
def promote_students(request):
    if request.method == 'POST':
        form = StudentPromotionForm(request.POST)
        if form.is_valid():
            from_level = form.cleaned_data['from_level']
            to_level = form.cleaned_data['to_level']
            academic_year = form.cleaned_data['academic_year']

            # Get students eligible for promotion
            results = StudentResult.objects.filter(
                level=from_level,
                academic_year=academic_year
            ).exclude(result='باقي للإعادة')

            promoted_count = 0
            for result in results:
                student = result.student

                # Update student record
                student.level = to_level
                student.previous_seat_number = student.current_seat_number
                # Here we can generate a new seat number or keep it as is for now
                student.enrollment_status = 'منقول' if result.result == 'ناجح ومنقول' else 'منقول بمواد'
                student.save()

                promoted_count += 1

            messages.success(
                request,
                _(f'تم ترقية {promoted_count} طالب من {from_level} إلى {to_level}')
            )
            return redirect('students:student_list')
    else:
        form = StudentPromotionForm()

    return render(request, 'students/promote_students.html', {'form': form})

@login_required
def export_student_list(request):
    level_id = request.GET.get('level')

    # Create the response and CSV writer
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="students.csv"'
    response.write('\ufeff')  # BOM for Excel to recognize UTF-8

    writer = csv.writer(response)
    writer.writerow([
        'الكود', 'رقم الجلوس الحالي', 'رقم الجلوس السابق',
        'الاسم', 'النوع', 'الرقم القومي', 'رقم التليفون',
        'المرحلة', 'المستوى', 'حالة القيد'
    ])

    # Get students data
    students = Student.objects.all()
    if level_id:
        students = students.filter(level_id=level_id)

    for student in students:
        writer.writerow([
            student.code,
            student.current_seat_number,
            student.previous_seat_number or '',
            student.full_name,
            student.gender,
            student.national_id,
            student.phone_number,
            student.level.stage.name,
            student.level.name,
            student.enrollment_status
        ])

    return response

@login_required
def export_course_results(request):
    """تصدير نتائج المواد"""
    # الحصول على المعايير من GET parameters
    course_id = request.GET.get('course')
    level_id = request.GET.get('level')
    academic_year = request.GET.get('academic_year')

    # بناء الاستعلام
    results = CourseResult.objects.all()

    if course_id:
        results = results.filter(course_id=course_id)
    if level_id:
        results = results.filter(student__level_id=level_id)
    if academic_year:
        results = results.filter(academic_year=academic_year)

    # إنشاء ملف CSV
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="course_results.csv"'
    response.write('\ufeff')  # BOM for Excel to recognize UTF-8

    writer = csv.writer(response)
    writer.writerow([
        'كود الطالب', 'اسم الطالب', 'المادة', 'الدرجة',
        'السنة الدراسية', 'المرحلة', 'المستوى'
    ])

    for result in results:
        writer.writerow([
            result.student.code,
            result.student.full_name,
            result.course.name,
            result.score,
            result.academic_year,
            result.student.level.stage.name,
            result.student.level.name
        ])

    return response

@login_required
def export_results(request, pk):
    level = get_object_or_404(Level, pk=pk)
    academic_year = request.GET.get('academic_year')

    if not academic_year:
        messages.error(request, _('يرجى تحديد السنة الدراسية'))
        return redirect('students:results_list')

    # Create the response and CSV writer
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="results_{level.name}_{academic_year}.csv"'
    response.write('\ufeff')  # BOM for Excel to recognize UTF-8

    writer = csv.writer(response)

    # Get all courses for this level
    courses = Course.objects.filter(level=level)

    # Create headers
    headers = ['الكود', 'رقم الجلوس', 'الاسم']
    for course in courses:
        headers.append(course.name)
    headers.extend(['المجموع', 'النتيجة', 'ملاحظات'])

    writer.writerow(headers)

    # Get results for all students
    results = StudentResult.objects.filter(level=level, academic_year=academic_year)

    for result in results:
        student = result.student
        row = [student.code, student.current_seat_number, student.full_name]

        # Get scores for each course
        total_score = 0
        for course in courses:
            try:
                course_result = CourseResult.objects.get(
                    student=student,
                    course=course,
                    academic_year=academic_year
                )
                score = course_result.score
                total_score += score
                row.append(score)
            except CourseResult.DoesNotExist:
                row.append('غ')

        # Add total and result
        row.append(total_score)
        row.append(result.result)

        # Add notes (failed courses)
        failed_courses = result.failed_courses.all()
        if failed_courses:
            notes = ', '.join([course.name for course in failed_courses])
            row.append(notes)
        else:
            row.append('')

        writer.writerow(row)

    return response

# AJAX Views
def load_levels(request):
    stage_id = request.GET.get('stage_id')
    levels = Level.objects.filter(stage_id=stage_id).order_by('name')
    return render(request, 'students/level_dropdown_list_options.html', {'levels': levels})

# دالة البحث عن طالب
@login_required
def search_student(request):
    query = request.GET.get('q', '')
    students = None

    if query:
        # البحث في الاسم والكود ورقم الجلوس والرقم القومي
        students = Student.objects.filter(
            Q(full_name__icontains=query) |
            Q(code__icontains=query) |
            Q(current_seat_number__icontains=query) |
            Q(national_id__icontains=query)
        )

    context = {
        'students': students,
        'query': query,
    }

    return render(request, 'students/search_results.html', context)

# تصدير بيانات الطلاب
@login_required
def export_students(request):
    level_id = request.GET.get('level_id')
    academic_year = request.GET.get('academic_year')

    if level_id:
        students = Student.objects.filter(level_id=level_id)
    else:
        students = Student.objects.all()

    response = export_students_to_excel(students, academic_year)
    return response

# تصدير درجات طالب معين
@login_required
def export_student_results(request, pk):
    student = get_object_or_404(Student, pk=pk)
    academic_year = request.GET.get('academic_year')

    response = export_results_to_excel(student, academic_year)
    return response

# طباعة إفادة قيد لطالب
@login_required
def print_student_certificate(request, pk):
    student = get_object_or_404(Student, pk=pk)

    response = export_student_certificate(student)
    return response

# استيراد بيانات الطلاب
@login_required
def import_students(request):
    if request.method == 'POST':
        form = ImportStudentsForm(request.POST, request.FILES)
        if form.is_valid():
            file = request.FILES['file']
            update_existing = form.cleaned_data['update_existing']

            success, message = import_students_from_excel(file, update_existing)

            if success:
                messages.success(request, message)
            else:
                messages.error(request, message)

            return redirect('students:student_list')
    else:
        form = ImportStudentsForm()

    context = {
        'form': form,
    }

    return render(request, 'students/import_students.html', context)

# استيراد نتائج الطلاب
@login_required
def import_results(request):
    if request.method == 'POST':
        form = ImportResultsForm(request.POST, request.FILES)
        if form.is_valid():
            file = request.FILES['file']
            academic_year = form.cleaned_data['academic_year']

            success, message = import_results_from_excel(file, academic_year)

            if success:
                messages.success(request, message)
            else:
                messages.error(request, message)

            return redirect('students:course_result_list')
    else:
        form = ImportResultsForm()

    context = {
        'form': form,
    }

    return render(request, 'students/import_results.html', context)

# حذف جميع الطلاب
@login_required
def delete_all_students(request):
    if request.method == 'POST':
        # التحقق من كلمة المرور للتأكيد
        password_confirmation = request.POST.get('password_confirmation')

        if password_confirmation == 'DELETE_ALL_STUDENTS':
            # حذف جميع الطلاب
            students_count = Student.objects.count()
            Student.objects.all().delete()

            messages.success(request, f'تم حذف {students_count} طالب بنجاح')
            return redirect('students:student_list')
        else:
            messages.error(request, 'كلمة التأكيد غير صحيحة. يجب كتابة "DELETE_ALL_STUDENTS" بالضبط')

    # عرض صفحة التأكيد
    students_count = Student.objects.count()
    context = {
        'students_count': students_count,
    }

    return render(request, 'students/delete_all_students.html', context)