# نظام إدارة طلاب رواق العلوم الشرعية والعربية بالأزهر الشريف

نظام ويب متكامل لإدارة طلاب رواق العلوم الشرعية والعربية بالأزهر الشريف، يدعم إدارة الطلاب، والاختبارات، والنتائج، والترقية بين المستويات.

## المميزات الرئيسية

- إدارة بيانات الطلاب (إضافة، تعديل، حذف، عرض)
- تسجيل نتائج الطلاب في المواد المختلفة
- إنشاء النتائج النهائية وتحديد حالة الطالب (ناجح، منقول بمواد، باقي للإعادة)
- ترقية الطلاب بين المستويات المختلفة
- تصدير بيانات الطلاب والنتائج بصيغة CSV
- واجهة مستخدم سهلة الاستخدام بالكامل باللغة العربية
- دعم التصميم المستجيب للعمل على جميع أحجام الشاشات

## المتطلبات الأساسية

- Python 3.8+
- Django 4.2+
- وحدات Python الإضافية المذكورة في ملف requirements.txt

## طريقة الإعداد

1. قم بتنزيل أو استنساخ هذا المستودع:
```
git clone https://github.com/your-username/azhar-student-management.git
cd azhar-student-management
```

2. قم بإنشاء وتفعيل بيئة Python افتراضية:
```
python -m venv venv
source venv/bin/activate  # لنظام Linux/Mac
venv\Scripts\activate     # لنظام Windows
```

3. قم بتثبيت المتطلبات:
```
pip install -r requirements.txt
```

4. قم بإجراء عمليات الترحيل الأولية لقاعدة البيانات:
```
python manage.py makemigrations
python manage.py migrate
```

5. قم بإنشاء حساب مستخدم للمشرف:
```
python manage.py createsuperuser
```

6. قم بتشغيل خادم التطوير:
```
python manage.py runserver
```

7. افتح المتصفح على العنوان [http://127.0.0.1:8000](http://127.0.0.1:8000)

## هيكل المشروع

- `students/` - تطبيق Django الرئيسي لإدارة الطلاب
  - `models.py` - نماذج البيانات
  - `views.py` - وظائف العرض والمعالجة
  - `forms.py` - نماذج الإدخال
  - `urls.py` - تكوين عناوين URL
- `templates/` - قوالب HTML
- `static/` - الملفات الثابتة (CSS, JS, الصور)

## هيكل البيانات

النظام يدعم المراحل والمستويات التالية حسب نظام رواق العلوم الشرعية والعربية:

- المراحل بالترتيب: (تمهيدية – متوسطة – تخصصية)
  - التمهيدية (مستوى أول – مستوى ثان)
  - المتوسطة (مستوى أول – مستوى ثانى)
  - التخصصية أربع تخصصات لكل تخصص أربع مستويات (فقه – تفسير وحديث – عقيدة – لغة عربية)

## نظام النتائج والترقية

- الدرجة النهائية لكل مادة هي 100 والنجاح من 50 فأكثر
- يحق للطالب الراسب في مادتين أو مادة واحدة التصعيد للمستوى الأعلى (منقول بمواد)
- في حالة رسوب الطالب في أكثر من مادتين يبقى بنفس المستوى (باقي للإعادة)
- في حالة نجاح الطالب في جميع المواد تكون النتيجة (ناجح ومنقول) للمستوى التالي

## المساهمة في المشروع

نرحب بمساهماتكم لتحسين هذا النظام. يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد لميزتك (`git checkout -b feature/amazing-feature`)
3. قم بتأكيد تغييراتك (`git commit -m 'Add some amazing feature'`)
4. قم بدفع الفرع (`git push origin feature/amazing-feature`)
5. قم بفتح طلب سحب 