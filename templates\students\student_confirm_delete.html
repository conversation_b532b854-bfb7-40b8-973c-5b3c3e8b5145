{% extends 'base.html' %}
{% load static %}

{% block title %}تأكيد حذف الطالب - نظام إدارة طلاب رواق العلوم الشرعية والعربية{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h2 mb-0 text-danger fw-bold">
                <i class="fas fa-trash-alt me-2"></i>تأكيد حذف الطالب
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'students:student_list' %}">قائمة الطلاب</a></li>
                    <li class="breadcrumb-item active" aria-current="page">حذف طالب</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{% url 'students:student_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة لقائمة الطلاب
            </a>
        </div>
    </div>

    <div class="card shadow-sm border-0">
        <div class="card-body p-4">
            <div class="alert alert-danger">
                <h4 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>تحذير!
                </h4>
                <p>هل أنت متأكد من رغبتك في حذف الطالب "{{ student.full_name }}" ({{ student.student_code }})؟</p>
                <p class="mb-0">هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع البيانات المتعلقة بهذا الطالب بما في ذلك النتائج الدراسية.</p>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات الطالب
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>الكود:</strong> {{ student.student_code }}
                            </div>
                            <div class="mb-2">
                                <strong>الاسم:</strong> {{ student.full_name }}
                            </div>
                            <div class="mb-2">
                                <strong>المرحلة:</strong> {{ student.stage.name }}
                            </div>
                            <div class="mb-2">
                                <strong>المستوى:</strong> {{ student.level.name }}
                            </div>
                            <div class="mb-0">
                                <strong>حالة القيد:</strong> 
                                <span class="badge {% if student.enrollment_status == 'مقيد' %}bg-success{% elif student.enrollment_status == 'محول' %}bg-primary{% elif student.enrollment_status == 'مفصول' %}bg-danger{% else %}bg-warning{% endif %}">
                                    {{ student.enrollment_status }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <form method="post">
                {% csrf_token %}
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <a href="{% url 'students:student_list' %}" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-times-circle"></i> إلغاء
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash-alt"></i> تأكيد الحذف
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 