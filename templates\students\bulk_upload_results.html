{% extends 'base.html' %}
{% load static %}

{% block title %}رفع نتائج بالجملة - نظام إدارة طلاب رواق العلوم الشرعية والعربية{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h2 mb-0 text-primary fw-bold">
                <i class="fas fa-upload me-2"></i>رفع نتائج بالجملة
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'students:course_result_list' %}">نتائج المواد</a></li>
                    <li class="breadcrumb-item active" aria-current="page">رفع نتائج بالجملة</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{% url 'students:course_result_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة لنتائج المواد
            </a>
        </div>
    </div>

    <div class="card shadow-sm border-0 mb-4">
        <div class="card-body p-4">
            <h5 class="card-title mb-4">
                <i class="fas fa-file-upload me-2"></i>رفع ملف نتائج
            </h5>

            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.course.id_for_label }}" class="form-label">المادة الدراسية</label>
                        {{ form.course }}
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="{{ form.academic_year.id_for_label }}" class="form-label">العام الدراسي</label>
                        {{ form.academic_year }}
                    </div>
                </div>
                <div class="mb-3">
                    <label for="{{ form.results_file.id_for_label }}" class="form-label">ملف النتائج (CSV أو Excel)</label>
                    {{ form.results_file }}
                    <div class="form-text text-muted">
                        يجب أن يحتوي الملف على أعمدة: code, score
                    </div>
                </div>
                <div class="mb-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>رفع النتائج
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow-sm border-0">
        <div class="card-header bg-light py-3">
            <h5 class="mb-0">
                <i class="fas fa-info-circle me-2"></i>تعليمات رفع النتائج
            </h5>
        </div>
        <div class="card-body">
            <div class="mb-4">
                <h6 class="fw-bold">تنسيق الملف:</h6>
                <ul>
                    <li>يدعم النظام ملفات Excel أو CSV.</li>
                    <li>يجب أن يحتوي الملف على الأعمدة التالية:
                        <ul>
                            <li><strong>code</strong>: كود الطالب</li>
                            <li><strong>score</strong>: الدرجة</li>
                        </ul>
                    </li>
                </ul>
            </div>
            <div class="mb-4">
                <h6 class="fw-bold">ملاحظات هامة:</h6>
                <ul>
                    <li>يجب التأكد من صحة أكواد الطلاب.</li>
                    <li>سيتم تحديث النتائج الموجودة مسبقاً للطلاب في حال كانت متوفرة.</li>
                    <li>سيتم إبلاغك بالأخطاء في حال وجودها أثناء معالجة الملف.</li>
                </ul>
            </div>
            <div>
                <a href="{% static 'examples/results_template.xlsx' %}" class="btn btn-outline-success">
                    <i class="fas fa-download me-2"></i>تحميل نموذج لملف النتائج
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 