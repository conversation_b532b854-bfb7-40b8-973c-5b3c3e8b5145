{% extends 'base.html' %}
{% load static %}

{% block title %}رفع نتائج بالجملة - نظام إدارة طلاب رواق العلوم الشرعية والعربية{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h2 mb-0 text-primary fw-bold">
                <i class="fas fa-upload me-2"></i>رفع نتائج بالجملة
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'students:course_result_list' %}">نتائج المواد</a></li>
                    <li class="breadcrumb-item active" aria-current="page">رفع نتائج بالجملة</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{% url 'students:course_result_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة لنتائج المواد
            </a>
        </div>
    </div>

    <div class="card shadow-sm border-0 mb-4">
        <div class="card-body p-4">
            <h5 class="card-title mb-4">
                <i class="fas fa-file-upload me-2"></i>رفع ملف نتائج
            </h5>

            <form method="post" enctype="multipart/form-data" id="uploadForm">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="{{ form.stage.id_for_label }}" class="form-label">المرحلة</label>
                        {{ form.stage }}
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="{{ form.level.id_for_label }}" class="form-label">المستوى</label>
                        {{ form.level }}
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="{{ form.academic_year.id_for_label }}" class="form-label">العام الدراسي</label>
                        {{ form.academic_year }}
                    </div>
                </div>

                <!-- زر تصدير النموذج -->
                <div class="mb-3">
                    <button type="button" class="btn btn-success" id="exportTemplateBtn" disabled>
                        <i class="fas fa-download me-2"></i>تصدير نموذج النتائج
                    </button>
                    <small class="text-muted d-block mt-1">اختر المرحلة والمستوى لتفعيل زر التصدير</small>
                </div>

                <div class="mb-3">
                    <label for="{{ form.results_file.id_for_label }}" class="form-label">ملف النتائج (Excel)</label>
                    {{ form.results_file }}
                    <div class="form-text text-muted">
                        استخدم النموذج المصدر أعلاه لضمان التنسيق الصحيح
                    </div>
                </div>

                <div class="mb-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>رفع النتائج
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow-sm border-0">
        <div class="card-header bg-light py-3">
            <h5 class="mb-0">
                <i class="fas fa-info-circle me-2"></i>تعليمات رفع النتائج
            </h5>
        </div>
        <div class="card-body">
            <div class="mb-4">
                <h6 class="fw-bold">طريقة العمل:</h6>
                <ol>
                    <li>اختر <strong>المرحلة</strong> و<strong>المستوى</strong> المطلوب</li>
                    <li>اضغط على <strong>"تصدير نموذج النتائج"</strong> لتحميل ملف يحتوي على:</li>
                    <ul>
                        <li>جميع طلاب المستوى مع أرقام جلوسهم</li>
                        <li>جميع مواد المستوى في أعمدة منفصلة</li>
                    </ul>
                    <li>املأ درجات الطلاب في الملف المحمل</li>
                    <li>ارفع الملف بعد ملئه</li>
                </ol>
            </div>
            <div class="mb-4">
                <h6 class="fw-bold">ملاحظات هامة:</h6>
                <ul>
                    <li><strong>لا تغير</strong> أرقام الجلوس أو أسماء الطلاب في الملف</li>
                    <li>املأ الدرجات فقط في الأعمدة المخصصة للمواد</li>
                    <li>الدرجات يجب أن تكون أرقام صحيحة ضمن الحد الأقصى للمادة</li>
                    <li>اترك الخلية فارغة إذا لم يحضر الطالب الامتحان</li>
                </ul>
            </div>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> يتم الربط بين الطلاب والنتائج من خلال <strong>رقم الجلوس</strong> وليس الكود.
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث المستويات عند تغيير المرحلة
    $('#id_stage').change(function() {
        var stageId = $(this).val();
        var levelSelect = $('#id_level');
        var exportBtn = $('#exportTemplateBtn');

        if (stageId) {
            $.ajax({
                url: '{% url "students:ajax_load_levels" %}',
                data: {
                    'stage_id': stageId
                },
                success: function(data) {
                    levelSelect.html(data);
                    exportBtn.prop('disabled', true);
                },
                error: function() {
                    levelSelect.html('<option value="">خطأ في تحميل المستويات</option>');
                    exportBtn.prop('disabled', true);
                }
            });
        } else {
            levelSelect.html('<option value="">اختر المرحلة أولاً</option>');
            exportBtn.prop('disabled', true);
        }
    });

    // تفعيل زر التصدير عند اختيار المستوى
    $('#id_level').change(function() {
        var levelId = $(this).val();
        var exportBtn = $('#exportTemplateBtn');

        if (levelId) {
            exportBtn.prop('disabled', false);
        } else {
            exportBtn.prop('disabled', true);
        }
    });

    // تصدير النموذج
    $('#exportTemplateBtn').click(function() {
        var stageId = $('#id_stage').val();
        var levelId = $('#id_level').val();
        var academicYear = $('#id_academic_year').val();

        if (levelId) {
            var url = '{% url "students:bulk_upload_results" %}?export_template=1&stage=' + stageId + '&level=' + levelId;
            if (academicYear) {
                url += '&academic_year=' + encodeURIComponent(academicYear);
            }
            window.location.href = url;
        } else {
            alert('يرجى اختيار المرحلة والمستوى أولاً');
        }
    });

    // تحقق من القيم الموجودة عند تحميل الصفحة
    var initialStageId = $('#id_stage').val();
    var initialLevelId = $('#id_level').val();

    if (initialStageId && initialLevelId) {
        $('#exportTemplateBtn').prop('disabled', false);
    }
});
</script>
{% endblock %}