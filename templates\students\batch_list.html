{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة الدفعات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إدارة الدفعات</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{% url 'students:batch_add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة دفعة جديدة
            </a>
        </div>
    </div>

    <!-- Batches List -->
    <div class="card shadow-sm border-0">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-users"></i> قائمة الدفعات
            </h5>
        </div>
        <div class="card-body">
            {% if batches %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>اسم الدفعة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>الحالة</th>
                            <th>عدد الطلاب</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for batch in batches %}
                        <tr>
                            <td class="fw-bold">{{ batch.name }}</td>
                            <td>
                                {% if batch.start_date %}
                                    {{ batch.start_date|date:"Y-m-d" }}
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if batch.end_date %}
                                    {{ batch.end_date|date:"Y-m-d" }}
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if batch.is_active %}
                                    <span class="badge bg-success">نشطة</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشطة</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ batch.student_set.count }}</span>
                            </td>
                            <td>{{ batch.created_at|date:"Y-m-d H:i" }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'students:batch_edit' batch.pk %}" 
                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'students:batch_delete' batch.pk %}" 
                                       class="btn btn-sm btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد دفعات</h5>
                <p class="text-muted">ابدأ بإضافة دفعة جديدة</p>
                <a href="{% url 'students:batch_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة دفعة جديدة
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
