# Generated by Django 4.2.20 on 2025-05-25 23:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Batch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الدفعة')),
                ('start_date', models.DateField(blank=True, null=True, verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ النهاية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'الدفعة',
                'verbose_name_plural': 'الدفعات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Level',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('المستوى الأول', 'المستوى الأول'), ('المستوى الثاني', 'المستوى الثاني'), ('المستوى الثالث', 'المستوى الثالث'), ('المستوى الرابع', 'المستوى الرابع')], max_length=50, verbose_name='اسم المستوى')),
            ],
            options={
                'verbose_name': 'المستوى الدراسي',
                'verbose_name_plural': 'المستويات الدراسية',
            },
        ),
        migrations.CreateModel(
            name='Stage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('تمهيدية', 'تمهيدية'), ('متوسطة', 'متوسطة'), ('تخصصية فقه واصوله', 'تخصصية فقه واصوله'), ('تخصصية تفسير وحديث', 'تخصصية تفسير وحديث'), ('تخصصية لغة عربية', 'تخصصية لغة عربية'), ('تخصصية عقيدة', 'تخصصية عقيدة'), ('شعبة عامة متقدمة', 'شعبة عامة متقدمة')], max_length=50, verbose_name='المرحلة')),
            ],
            options={
                'verbose_name': 'المرحلة الدراسية',
                'verbose_name_plural': 'المراحل الدراسية',
            },
        ),
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='الكود')),
                ('previous_seat_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الجلوس السابق')),
                ('current_seat_number', models.CharField(max_length=20, verbose_name='رقم الجلوس الحالي')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم رباعي')),
                ('gender', models.CharField(choices=[('ذكر', 'ذكر'), ('أنثى', 'أنثى')], max_length=5, verbose_name='النوع')),
                ('national_id', models.CharField(max_length=14, unique=True, verbose_name='الرقم القومي')),
                ('phone_number', models.CharField(max_length=20, verbose_name='رقم التليفون')),
                ('governorate', models.CharField(choices=[('القاهرة', 'القاهرة'), ('الجيزة', 'الجيزة'), ('الإسكندرية', 'الإسكندرية'), ('الدقهلية', 'الدقهلية'), ('البحر الأحمر', 'البحر الأحمر'), ('البحيرة', 'البحيرة'), ('الفيوم', 'الفيوم'), ('الغربية', 'الغربية'), ('الإسماعيلية', 'الإسماعيلية'), ('المنوفية', 'المنوفية'), ('المنيا', 'المنيا'), ('القليوبية', 'القليوبية'), ('الوادي الجديد', 'الوادي الجديد'), ('السويس', 'السويس'), ('اسوان', 'اسوان'), ('اسيوط', 'اسيوط'), ('بني سويف', 'بني سويف'), ('بورسعيد', 'بورسعيد'), ('دمياط', 'دمياط'), ('الشرقية', 'الشرقية'), ('جنوب سيناء', 'جنوب سيناء'), ('كفر الشيخ', 'كفر الشيخ'), ('مطروح', 'مطروح'), ('الأقصر', 'الأقصر'), ('قنا', 'قنا'), ('شمال سيناء', 'شمال سيناء'), ('سوهاج', 'سوهاج'), ('أخرى', 'أخرى')], default='القاهرة', max_length=20, verbose_name='المحافظة')),
                ('vision_status', models.CharField(choices=[('مبصر', 'مبصر'), ('كفيف', 'كفيف')], max_length=10, verbose_name='حالة البصر')),
                ('special_needs', models.BooleanField(default=False, verbose_name='من ذوي الهمم')),
                ('madhhab', models.CharField(choices=[('حنفي', 'حنفي'), ('مالكي', 'مالكي'), ('شافعي', 'شافعي'), ('حنبلي', 'حنبلي')], max_length=10, verbose_name='المذهب الفقهي')),
                ('study_type', models.CharField(choices=[('مباشر', 'مباشر'), ('عن بعد', 'عن بعد')], max_length=10, verbose_name='نوع الدراسة')),
                ('enrollment_status', models.CharField(choices=[('مستجد', 'مستجد'), ('منقول', 'منقول'), ('منقول بمواد', 'منقول بمواد'), ('باقي للإعادة', 'باقي للإعادة')], max_length=20, verbose_name='حالة القيد')),
                ('batch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='students.batch', verbose_name='الدفعة')),
                ('level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.level', verbose_name='المستوى')),
            ],
            options={
                'verbose_name': 'الطالب',
                'verbose_name_plural': 'الطلاب',
            },
        ),
        migrations.AddField(
            model_name='level',
            name='stage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.stage', verbose_name='المرحلة'),
        ),
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المادة')),
                ('max_score', models.PositiveIntegerField(default=100, verbose_name='الدرجة النهائية')),
                ('pass_score', models.PositiveIntegerField(default=50, verbose_name='درجة النجاح')),
                ('level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='courses', to='students.level', verbose_name='المستوى')),
            ],
            options={
                'verbose_name': 'المادة الدراسية',
                'verbose_name_plural': 'المواد الدراسية',
            },
        ),
        migrations.CreateModel(
            name='StudentResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('result', models.CharField(choices=[('ناجح', 'ناجح'), ('ناجح ومنقول', 'ناجح ومنقول'), ('منقول بمواد', 'منقول بمواد'), ('باقي للإعادة', 'باقي للإعادة')], max_length=20, verbose_name='النتيجة')),
                ('batch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.batch', verbose_name='الدفعة')),
                ('failed_courses', models.ManyToManyField(blank=True, to='students.course', verbose_name='المواد المتبقية')),
                ('level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.level', verbose_name='المستوى')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='final_results', to='students.student', verbose_name='الطالب')),
            ],
            options={
                'verbose_name': 'النتيجة النهائية',
                'verbose_name_plural': 'النتائج النهائية',
                'unique_together': {('student', 'level', 'batch')},
            },
        ),
        migrations.CreateModel(
            name='CourseResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.PositiveIntegerField(verbose_name='الدرجة')),
                ('batch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.batch', verbose_name='الدفعة')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.course', verbose_name='المادة')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='results', to='students.student', verbose_name='الطالب')),
            ],
            options={
                'verbose_name': 'نتيجة مادة',
                'verbose_name_plural': 'نتائج المواد',
                'unique_together': {('student', 'course', 'batch')},
            },
        ),
    ]
